<?php if(session('success') || session('error') || session('warning') || session('info')): ?>
    <div class="notification-container">
        <?php if(session('success')): ?>
            <div class="notification success">
                <span><?php echo e(session('success')); ?></span>
                <button type="button" class="close-btn" onclick="this.parentElement.remove()">×</button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="notification error">
                <span><?php echo e(session('error')); ?></span>
                <button type="button" class="close-btn" onclick="this.parentElement.remove()">×</button>
            </div>
        <?php endif; ?>

        <?php if(session('warning')): ?>
            <div class="notification warning">
                <span><?php echo e(session('warning')); ?></span>
                <button type="button" class="close-btn" onclick="this.parentElement.remove()">×</button>
            </div>
        <?php endif; ?>

        <?php if(session('info')): ?>
            <div class="notification info">
                <span><?php echo e(session('info')); ?></span>
                <button type="button" class="close-btn" onclick="this.parentElement.remove()">×</button>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Auto-dismiss notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(function(notification) {
                setTimeout(function() {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 5000);
            });
        });
    </script>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive - St. Paul University Philippines\Desktop\CAPSTONE\System\Thesis\resources\views/components/notification.blade.php ENDPATH**/ ?>